import axios from 'axios';
import { API_URL } from '../utils/constant';
import { getToken } from '../utils/storage';

const http = axios.create({
    baseURL: API_URL,
    headers: {
        'Content-type': 'application/json',
        Accept: 'application/json',
    },
});

http.interceptors.request.use(
    async function (config) {
        if (config.url !== '/user/refreshToken') {
            const accessToken = await getToken(config.url !== '/user/logout');
            config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
    },
    function (error) {
        return Promise.reject(error);
    }
);

http.interceptors.response.use(
    async function (response) {
        return response;
    },
    function (error) {
        return Promise.reject(error);
    }
);

export default http;
