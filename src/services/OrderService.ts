// const scrape = async (html: string, url: string, siteId: number) => {
//     const { data } = await http.post<ApiResponse<Product[]>>('/productExternal/scrape', { html, url, siteId });
//     return data;
// };

import { ApiResponse, Order, Product } from '../types';
import http from './http';

const getListDraff = async () => {
    const { data } = await http.get<ApiResponse<{ items: Order[] }>>('/order/draff');
    return data;
};

const createEmpty = async () => {
    const { data } = await http.get<ApiResponse<Order>>('/order/empty');
    return data;
};

const addCart = async (products: Product[], orderId: number) => {
    const { data } = await http.post<ApiResponse<{}>>(`/order/addCart/${orderId}`, { products });
    return data;
};

const OrderService = {
    //scrape,
    getListDraff,
    createEmpty,
    addCart,
};

export default OrderService;
