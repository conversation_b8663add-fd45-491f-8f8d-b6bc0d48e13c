import { ApiResponse, User } from '../types';
import http from './http';

const login = async (userName: string, userPass: string) => {
    const { data } = await http.post<ApiResponse<User>>('/user/login', { userName, userPass, isCustomer: 0 });
    return data;
};

const logout = async () => {
    const { data } = await http.get<ApiResponse<{}>>('/user/logout');
    return data;
};

const getProfile = async () => {
    const { data } = await http.get<ApiResponse<User>>('user/profile');
    return data;
};

const getToken = async (refreshToken: string) => {
    const { data } = await http.get<ApiResponse<User>>('/user/refreshToken', {
        headers: {
            Authorization: `Bearer ${refreshToken}`,
        },
    });
    return data;
};

const UserService = {
    login,
    logout,
    getProfile,
    getToken,
};

export default UserService;
