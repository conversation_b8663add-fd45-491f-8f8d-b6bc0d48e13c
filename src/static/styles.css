.font16, .font16 input {
    font-size: 16px !important;
}
.font20 {
    font-size: 20px !important;
}
.panelPrice {
    border: 3px solid #ddd;
    border-radius: 10px;
    display: flex;
    margin-top: 10px;
}

.panelPrice .column {
    /*flex: 1;*/
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
}

.panelPrice .title {
    color: #000;
    font-size: 1.2em;
}

.panelPrice .value {
    color: rgb(240, 88, 39);
    font-size: 1.5em;
}

.panelPrice .value a, .panelPrice .value a:hover, .panelPrice .value a:active {
    color: rgb(240, 88, 39);
    font-size: 1em;
}

.crossLine {
    text-decoration: line-through;
}

.dh86MainProduct {
    padding: 10px;
    background-color: #fff;
    max-height: 500px;
    overflow: scroll;
}

.dh86MainProduct h1 {
    font-size: 20px;
}

.dh86MainProduct table {
    width: 100%;
    /*margin-top: 30px;*/
}

.dh86MainProduct table td {
    padding-top: 10px;
    padding-bottom: 10px;
}

.dh86MainProduct td img {
    border-radius: 2px;
    height: 28px;
    margin-right: 10px;
    width: 28px;
}

.dh86MainProduct .attrName {
    align-items: center;
    color: #7a7a7a;
    font-size: 1.2em;
    width: 100px;
}

.dh86MainProduct .attrValue {
    align-items: center;
    background-color: #fff;
    border: 1px solid #dadde0;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    flex-direction: row;
    margin: 0 4px 8px;
    padding: 2px;
    position: relative;
    font-size: 1.2em;
    color: #000;
}

.dh86MainProduct .attrValue.active {
    border-color: #ff5000;
    color: #ff5000;
}

.cursor-pointer {
    cursor: pointer;
}