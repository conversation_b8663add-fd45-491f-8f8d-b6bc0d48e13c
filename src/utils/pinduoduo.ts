import { drop, join, split } from 'lodash';
import { ItemCost, Product } from '../types';
import { COST_TEXT, OLD_COST_TEXT } from './constant';

export const getPinduoduoPrices = () => {
    let price = -1,
        promotionPrice = 0;
    const prices: ItemCost[] = [];
    let priceWrappers = document.getElementsByClassName('_1vQZeIX1');
    if (priceWrappers.length > 0) {
        price = +(priceWrappers[0] as HTMLElement).innerText.trim();
        prices.push({
            label: COST_TEXT,
            cost: price,
        });
    } else {
        priceWrappers = document.getElementsByClassName('_1vQZeIX1W2_A3394vfWv9w');
        if (priceWrappers.length > 0) {
            price = +(priceWrappers[0] as HTMLElement).innerText.trim();
            prices.push({
                label: COST_TEXT,
                cost: price,
            });
        }
    }
    priceWrappers = document.getElementsByClassName('_1042IAQz');
    if (priceWrappers.length > 0) {
        promotionPrice = +(priceWrappers[0] as HTMLElement).innerText.replace('¥', '').trim();
        prices.push({
            label: OLD_COST_TEXT,
            cost: promotionPrice,
        });
    } else {
        priceWrappers = document.getElementsByClassName('_1042IAQzflFSJ51rGd3tOA');
        if (priceWrappers.length > 0) {
            promotionPrice = +(priceWrappers[0] as HTMLElement).innerText.replace('¥', '').trim();
            prices.push({
                label: OLD_COST_TEXT,
                cost: promotionPrice,
            });
        }
    }
    return {
        price,
        promotionPrice,
        prices,
    };
};

export const getPinduoduoProducts = (link: string, comment: string) => {
    const products: Product[] = [];
    let image = '',
        quantity = 1,
        price = -1,
        promotionPrice = 0,
        isCheckAttribute = false;
    const skuValues: string[] = [];
    let item = document.getElementsByClassName('_3jgmWyJT');
    if (item.length === 0) {
        item = document.getElementsByClassName('_3jgmWyJTgQqFWES06MSOup');
    }
    if (item.length > 0) {
        image = (item[0] as HTMLImageElement).currentSrc;
    }
    const wrapper = document.getElementsByClassName('sku-plus1');
    if (wrapper.length > 0 && (wrapper[0].getAttribute('style') ?? '').indexOf('block') > -1) {
        const infoWrapper = document.getElementsByClassName('nxbx3dAD');
        if (infoWrapper.length > 0) {
            item = infoWrapper[0].getElementsByTagName('img');
            if (item.length > 0) {
                image = (item[0] as HTMLImageElement).currentSrc;
            }
            item = infoWrapper[0].getElementsByClassName('_2Dfevmvi');
            if (item.length > 0) {
                quantity = +(item[0] as HTMLInputElement).value;
            }
            item = infoWrapper[0].getElementsByClassName('_27FaiT3N');
            if (item.length > 0) {
                const priceTxt = (item[0] as HTMLElement).innerText.trim();
                if (priceTxt.indexOf('-') > -1) {
                    const priceTxts = split(priceTxt, '-');
                    if (priceTxts.length > 0) {
                        price = +priceTxts[0].replace('¥', '');
                    }
                } else {
                    price = +priceTxt.replace('¥', '');
                }
            }
        }
        const skuItemWrappers = document.getElementsByClassName('_1XqP0nf5');
        if (skuItemWrappers.length > 0) {
            Array.from(skuItemWrappers).forEach((sku) => {
                item = sku.getElementsByClassName('_2bBCR7qt');
                if (item.length > 0) {
                    skuValues.push((item[0] as HTMLElement).innerText.trim());
                }
            });
            isCheckAttribute = skuItemWrappers.length !== skuValues.length;
        }
    } else {
        const priceData = getPinduoduoPrices();
        price = priceData.price;
        promotionPrice = priceData.promotionPrice;
    }
    if (price >= 0) {
        let shopCode = '',
            shopName = '';
        item = document.getElementsByClassName('_1g9X2Rjz');
        if (item.length > 0) {
            shopName = (item[0] as HTMLElement).innerText.trim();
        }
        const mallIDMatch = document.body.innerHTML.match(/"mallID":(\d+)/);
        if (mallIDMatch) {
            shopCode = 'https://mobile.yangkeduo.com/mall_page.html?mall_id=' + mallIDMatch[1];
        }
        products.push({
            shopCode,
            shopName,
            link,
            image,
            color: skuValues.length > 0 ? skuValues[0] : '',
            size: skuValues.length > 1 ? join(drop(skuValues), ',') : '',
            quantity,
            cost: price,
            promotionCost: promotionPrice,
            comment,
            isCheckAttribute,
        });
    }
    return products;
};
