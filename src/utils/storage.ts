import { User } from '../types';

export type UserKeys = keyof User;

export function setUserData(name: string, avatar: string, accessToken: string, refreshToken: string): Promise<void> {
    const value: User = { name, avatar, accessToken, refreshToken };
    return new Promise((resolve) => {
        chrome.storage.local.set(value, () => {
            resolve();
        });
    });
}

export function setToken(accessToken: string, refreshToken: string): Promise<void> {
    const value: User = { accessToken, refreshToken };
    return new Promise((resolve) => {
        chrome.storage.local.set(value, () => {
            resolve();
        });
    });
}

export function getUserData(): Promise<User> {
    const keys: UserKeys[] = ['id', 'name', 'avatar', 'accessToken', 'refreshToken'];
    return new Promise((resolve) => {
        chrome.storage.local.get(keys, (res: User) => {
            resolve(res);
        });
    });
}

export function getToken(isAccessToken): Promise<string> {
    const keys: UserKeys[] = isAccessToken ? ['accessToken'] : ['refreshToken'];
    return new Promise((resolve) => {
        chrome.storage.local.get(keys, (res: User) => {
            resolve((isAccessToken ? res.accessToken : res.refreshToken) ?? '');
        });
    });
}

export function removeUserData() {
    chrome.storage.local.clear();
}
