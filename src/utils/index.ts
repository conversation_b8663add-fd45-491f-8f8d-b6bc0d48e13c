import { drop, join, split } from 'lodash';
import { ItemCost, Product } from '../types';
import { COST_TEXT, OLD_COST_TEXT } from './constant';

export const getTTPrices = () => {
    let price = 0,
        promotionPrice = 0;
    const prices: ItemCost[] = [];
    const pw = document.getElementsByClassName('subPrice--PpThOLBO');
    if (pw.length > 0) {
        const span = pw[0].getElementsByClassName('text--fZ9NUhyQ');
        if (span.length > 0) {
            price = +(span[span.length - 1] as HTMLElement).innerText.trim();
            prices.push({
                label: COST_TEXT,
                cost: price,
            });
        }
        return {
            price,
            promotionPrice,
            prices,
        };
    }
    let priceWrappers = document.querySelectorAll('[class^="Price--priceText--"]');
    if (priceWrappers.length === 0) priceWrappers = document.querySelectorAll('[class^="priceText--"]');
    if (priceWrappers.length === 0) priceWrappers = document.querySelectorAll('[class="E7gD8doUq1--text--_4c1ce7d"]');
    if (priceWrappers.length > 0) {
        let i = 0,
            checkPriceQuestion = false;
        Array.from(priceWrappers).forEach((priceEle) => {
            if ((priceEle as HTMLElement).innerText.indexOf('?') > -1) {
                checkPriceQuestion = true;
                return false;
            } else {
                i++;
                if (i === 1) {
                    price = +(priceEle as HTMLElement).innerText.trim();
                    prices.push({
                        label: COST_TEXT,
                        cost: price,
                    });
                }
                if (i === 2) {
                    promotionPrice = +(priceEle as HTMLElement).innerText.trim();
                    prices.push({
                        label: OLD_COST_TEXT,
                        cost: promotionPrice,
                    });
                }
            }
        });
        if (checkPriceQuestion) {
            const pWrappers = document.getElementsByClassName('text--lKftOoXS');
            if (pWrappers.length > 0) {
                Array.from(pWrappers).forEach((pEle) => {
                    const priceTmp = +(pEle as HTMLElement).innerText.trim();
                    if (priceTmp > 0) {
                        price = priceTmp;
                        prices.push({
                            label: COST_TEXT,
                            cost: price,
                        });
                    }
                });
            }
        }
    } else {
        let priceWrappers = document.getElementsByClassName('tm-detail-meta');
        if (priceWrappers.length > 0) {
            const spans = priceWrappers[0].getElementsByClassName('tm-price');
            if (spans.length == 1) {
                price = +(spans[0] as HTMLElement).innerText.trim();
                prices.push({
                    label: COST_TEXT,
                    cost: price,
                });
            }
            if (spans.length == 2) {
                promotionPrice = +(spans[0] as HTMLElement).innerText.trim();
                prices.push({
                    label: OLD_COST_TEXT,
                    cost: promotionPrice,
                });
                price = +(spans[1] as HTMLElement).innerText.trim();
                prices.push({
                    label: COST_TEXT,
                    cost: price,
                });
            }
        } else {
            let priceWrappers = document.querySelectorAll('[class^="SecurityPrice--priceText--"]');
            if (priceWrappers.length > 0) {
                price = +(priceWrappers[0] as HTMLElement).innerText.trim();
                prices.push({
                    label: COST_TEXT,
                    cost: price,
                });
            } else {
                let priceWrappers = document.getElementsByClassName('tb-detail-price');
                if (priceWrappers.length > 0) {
                    let i = 0;
                    Array.from(priceWrappers).forEach((priceEle) => {
                        const spans = priceEle.getElementsByClassName('tb-rmb-num');
                        if (spans.length > 0) {
                            i++;
                            if (i === 1) {
                                const priceTxt = (spans[0] as HTMLElement).innerText.trim();
                                if (priceTxt.indexOf('-') > -1) {
                                    const priceTxts = split(priceTxt, '-');
                                    if (priceTxts.length > 1) {
                                        price = +priceTxts[0].trim();
                                        prices.push({
                                            label: COST_TEXT,
                                            cost: price,
                                            endCost: +priceTxts[1].trim(),
                                        });
                                    }
                                } else {
                                    price = +priceTxt;
                                    prices.push({
                                        label: COST_TEXT,
                                        cost: price,
                                    });
                                }
                            }
                            if (i === 2) {
                                promotionPrice = +(spans[0] as HTMLElement).innerText.trim();
                                prices.push({
                                    label: OLD_COST_TEXT,
                                    cost: promotionPrice,
                                });
                            }
                        }
                    });
                } else {
                    let span = document.getElementsByClassName('H7xLHMaqK8--text--_4c1ce7d');
                    if (span.length === 0) span = document.getElementsByClassName('Oo3vRXl7BS--text--_4c1ce7d');
                    if (span.length === 0) span = document.getElementsByClassName('Oo3vRXl7BS--text--c9c1f6b6');
                    if (span.length === 0) span = document.getElementsByClassName('fAIbwoPZg6--text--c9c1f6b6');
                    if (span.length === 0) span = document.getElementsByClassName('fAIbwoPZg6--text--_4c1ce7d');
                    if (span.length > 0) {
                        price = +(span[0] as HTMLElement).innerText.trim();
                        prices.push({
                            label: COST_TEXT,
                            cost: price,
                        });
                    }
                }
            }
        }
    }
    return {
        price,
        promotionPrice,
        prices,
    };
};

export const getTTSkuValues = () => {
    let image;
    let skuItemWrappers = document.getElementsByClassName('skuItemWrapper');
    let skuLength = skuItemWrappers.length;
    const skuValues: string[] = [];
    if (skuLength > 0) {
        Array.from(skuItemWrappers).forEach((sku) => {
            const skuItems = sku.getElementsByClassName('skuItem');
            Array.from(skuItems).forEach((skuItem) => {
                if (
                    skuItem.classList.contains('current') &&
                    skuItem.getElementsByClassName('skuValueName').length > 0
                ) {
                    skuValues.push(skuItem.getElementsByClassName('skuValueName')[0].textContent.trim());
                }
            });
        });
    } else {
        skuItemWrappers = document.getElementsByClassName('tm-sale-prop');
        skuLength = skuItemWrappers.length;
        if (skuLength > 0) {
            Array.from(skuItemWrappers).forEach((sku) => {
                const skuItems = sku.getElementsByTagName('li');
                if (skuItems.length > 0) {
                    Array.from(skuItems).forEach((skuItem) => {
                        if (skuItem.classList.contains('tb-selected') && skuItem.getElementsByTagName('a').length > 0) {
                            skuValues.push(skuItem.getElementsByTagName('a')[0].textContent.trim());
                        }
                    });
                }
            });
        } else {
            let skuItemWrappers = document.querySelectorAll('div[class*=" SkuContent--isSelected--"]');
            skuLength = skuItemWrappers.length;
            if (skuLength > 0) {
                Array.from(skuItemWrappers).forEach((sku) => {
                    const skuItems = sku.getElementsByTagName('span');
                    if (skuItems.length > 0) skuValues.push(skuItems[0].textContent.trim());
                });
            } else {
                let skuItemWrappers = document.getElementsByClassName('skuItem--uxMLmkRx');
                skuLength = skuItemWrappers.length;
                if (skuLength > 0) {
                    Array.from(skuItemWrappers).forEach((sku) => {
                        const skuItems = sku.getElementsByClassName('valueItem--GzWd2LsV');
                        Array.from(skuItems).forEach((skuItem) => {
                            if (
                                skuItem.classList.contains('isSelected--YrA6x4Yj') &&
                                skuItem.getElementsByTagName('span').length > 0
                            ) {
                                skuValues.push(skuItem.getElementsByTagName('span')[0].textContent.trim());
                                const images = skuItem.getElementsByTagName('img');
                                if (images.length > 0) image = images[0].currentSrc;
                            }
                        });
                    });
                } else {
                    let skuItemWrappers = document.getElementsByClassName('E7gD8doUq1--skuItem--_68c0cae');
                    skuLength = skuItemWrappers.length;
                    if (skuLength > 0) {
                        Array.from(skuItemWrappers).forEach((sku) => {
                            const skuItems = sku.getElementsByClassName('E7gD8doUq1--valueItem--ee898cc0');
                            Array.from(skuItems).forEach((skuItem) => {
                                if (
                                    skuItem.classList.contains('E7gD8doUq1--isSelected--be9dcb21') &&
                                    skuItem.getElementsByTagName('span').length > 0
                                ) {
                                    skuValues.push(skuItem.getElementsByTagName('span')[0].textContent.trim());
                                    const images = skuItem.getElementsByTagName('img');
                                    if (images.length > 0) image = images[0].currentSrc;
                                }
                            });
                        });
                    } else {
                        let skuItemWrappers = document.getElementsByClassName('QJEEHAN8H5--skuItem--_68c0cae');
                        skuLength = skuItemWrappers.length;
                        if (skuLength > 0) {
                            Array.from(skuItemWrappers).forEach((sku) => {
                                const skuItems = sku.getElementsByClassName('QJEEHAN8H5--valueItem--ee898cc0');
                                Array.from(skuItems).forEach((skuItem) => {
                                    if (
                                        skuItem.classList.contains('QJEEHAN8H5--isSelected--be9dcb21') &&
                                        skuItem.getElementsByTagName('span').length > 0
                                    ) {
                                        skuValues.push(skuItem.getElementsByTagName('span')[0].textContent.trim());
                                        const images = skuItem.getElementsByTagName('img');
                                        if (images.length > 0) image = images[0].currentSrc;
                                    }
                                });
                            });
                        } else {
                            let skuItemWrappers = document.getElementsByClassName('H7xLHMaqK8--skuItem--_68c0cae');
                            skuLength = skuItemWrappers.length;
                            if (skuLength > 0) {
                                Array.from(skuItemWrappers).forEach((sku) => {
                                    const skuItems = sku.getElementsByClassName('H7xLHMaqK8--valueItem--ee898cc0');
                                    Array.from(skuItems).forEach((skuItem) => {
                                        if (
                                            skuItem.classList.contains('H7xLHMaqK8--isSelected--be9dcb21') &&
                                            skuItem.getElementsByTagName('span').length > 0
                                        ) {
                                            skuValues.push(skuItem.getElementsByTagName('span')[0].textContent.trim());
                                            const images = skuItem.getElementsByTagName('img');
                                            if (images.length > 0) image = images[0].currentSrc;
                                        }
                                    });
                                });
                            }
                            else{
                                let skuItemWrappers = document.getElementsByClassName('Oo3vRXl7BS--skuItem--_68c0cae');
                                skuLength = skuItemWrappers.length;
                                if (skuLength > 0) {
                                    Array.from(skuItemWrappers).forEach((sku) => {
                                        const skuItems = sku.getElementsByClassName('Oo3vRXl7BS--valueItem--ee898cc0');
                                        Array.from(skuItems).forEach((skuItem) => {
                                            if (
                                                skuItem.classList.contains('Oo3vRXl7BS--isSelected--be9dcb21') &&
                                                skuItem.getElementsByTagName('span').length > 0
                                            ) {
                                                skuValues.push(skuItem.getElementsByTagName('span')[0].textContent.trim());
                                                const images = skuItem.getElementsByTagName('img');
                                                if (images.length > 0) image = images[0].currentSrc;
                                            }
                                        });
                                    });
                                }
                                else{
                                    let skuItemWrappers = document.getElementsByClassName('fAIbwoPZg6--skuItem--_68c0cae');
                                    skuLength = skuItemWrappers.length;
                                    if (skuLength > 0) {
                                        Array.from(skuItemWrappers).forEach((sku) => {
                                            const skuItems = sku.getElementsByClassName('fAIbwoPZg6--valueItem--ee898cc0');
                                            Array.from(skuItems).forEach((skuItem) => {
                                                if (
                                                    skuItem.classList.contains('fAIbwoPZg6--isSelected--be9dcb21') &&
                                                    skuItem.getElementsByTagName('span').length > 0
                                                ) {
                                                    skuValues.push(skuItem.getElementsByTagName('span')[0].textContent.trim());
                                                    const images = skuItem.getElementsByTagName('img');
                                                    if (images.length > 0) image = images[0].currentSrc;
                                                }
                                            });
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return {
        skuValues,
        skuLength,
        image,
    };
};

export const getTTImage = () => {
    let image = '';
    const item = document.getElementById('J_ImgBooth');
    if (item) image = (item as HTMLImageElement).currentSrc;
    else {
        let images = document.getElementsByClassName('PicGallery--mainPic--1eAqOie');
        if (images.length === 0) images = document.getElementsByClassName('PicGallery--thumbnailPic--1spSzep');
        if (images.length === 0) images = document.getElementsByClassName('PicGallery--mainPic--34u4Jrw');
        if (images.length === 0) images = document.getElementsByClassName('mainPic--zxTtQs0P');
        if (images.length === 0) images = document.getElementsByClassName('E7gD8doUq1--mainPic--_8729489');
        if (images.length === 0) images = document.getElementsByClassName('H7xLHMaqK8--mainPic--_8729489');
        if (images.length === 0) images = document.getElementsByClassName('Oo3vRXl7BS--mainPic--_8729489');
        if (images.length === 0) images = document.getElementsByClassName('fAIbwoPZg6--mainPic--_8729489');
        if (images.length > 0) image = (images[0] as HTMLImageElement).currentSrc;
    }
    return image;
};

export const getTTShop = () => {
    let shopCode = '',
        shopName = '';
    let shops = document.getElementsByClassName('ShopHeader--board--1nOkGuN');
    if (shops.length > 0) {
        shopCode = (shops[0] as HTMLLinkElement).href;
        const titles = shops[0].getElementsByClassName('ShopHeader--title--2qsBE1A');
        if (titles.length > 0) {
            shopName = titles[0].textContent.trim();
        }
    } else {
        shops = document.getElementsByClassName('shopLink');
        if (shops.length > 0) {
            shopCode = (shops[0] as HTMLLinkElement).href;
            shopName = shops[0].textContent.trim();
        } else {
            shops = document.getElementsByClassName('ShopHeaderNew--detailWrap--1fzaHpm');
            if (shops.length > 0) {
                shopCode = (shops[0] as HTMLLinkElement).href;
                shopName = shops[0].textContent.trim();
            } else {
                shops = document.getElementsByClassName('tb-shop-name');
                if (shops.length > 0) {
                    const links = shops[0].getElementsByTagName('a');
                    if (links.length > 0) {
                        shopCode = links[0].href;
                        shopName = links[0].textContent.trim();
                    }
                } else {
                    const links = document.querySelectorAll('a[class^="ShopHeader--detailWrap"]');
                    if (links.length > 0) {
                        shopCode = (links[0] as HTMLAnchorElement).href;
                        const spans = links[0].querySelectorAll('span[class^="ShopHeader--shopName"]');
                        if (spans.length > 0) shopName = spans[0].textContent.trim();
                    } else {
                        const links = document.querySelectorAll('a[class^="detailWrap--DLcmKM2Q"]');
                        if (links.length > 0) {
                            shopCode = (links[0] as HTMLAnchorElement).href;
                            const spans = links[0].querySelectorAll('span[class^="shopName--mTDZGIPO"]');
                            if (spans.length > 0) shopName = spans[0].textContent.trim();
                        } else {
                            const links = document.querySelectorAll('a[class="E7gD8doUq1--detailWrap--e9e2b47b"]');
                            if (links.length > 0) {
                                shopCode = (links[0] as HTMLAnchorElement).href;
                                const spans = links[0].querySelectorAll('span[class="E7gD8doUq1--shopName--ccf81bdd"]');
                                if (spans.length > 0) shopName = spans[0].textContent.trim();
                            } else {
                                const links = document.querySelectorAll('a[class="QJEEHAN8H5--detailWrap--e9e2b47b"]');
                                if (links.length > 0) {
                                    shopCode = (links[0] as HTMLAnchorElement).href;
                                    const spans = links[0].querySelectorAll(
                                        'span[class="QJEEHAN8H5--shopName--ccf81bdd"]'
                                    );
                                    if (spans.length > 0) shopName = spans[0].textContent.trim();
                                } else {
                                    const links = document.querySelectorAll(
                                        'a[class="H7xLHMaqK8--detailWrap--e9e2b47b"]'
                                    );
                                    if (links.length > 0) {
                                        shopCode = (links[0] as HTMLAnchorElement).href;
                                        const spans = links[0].querySelectorAll(
                                            'span[class="H7xLHMaqK8--shopName--ccf81bdd"]'
                                        );
                                        if (spans.length > 0) shopName = spans[0].textContent.trim();
                                    }
                                    else {
                                        const links = document.querySelectorAll(
                                            'a[class="Oo3vRXl7BS--detailWrap--e9e2b47b"]'
                                        );
                                        if (links.length > 0) {
                                            shopCode = (links[0] as HTMLAnchorElement).href;
                                            const spans = links[0].querySelectorAll(
                                                'span[class="Oo3vRXl7BS--shopName--ccf81bdd"]'
                                            );
                                            if (spans.length > 0) shopName = spans[0].textContent.trim();
                                        }
                                        else{
                                            const links = document.querySelectorAll(
                                                'a[class="fAIbwoPZg6--detailWrap--e9e2b47b"]'
                                            );
                                            if (links.length > 0) {
                                                shopCode = (links[0] as HTMLAnchorElement).href;
                                                const spans = links[0].querySelectorAll(
                                                    'span[class="fAIbwoPZg6--shopName--ccf81bdd"]'
                                                );
                                                if (spans.length > 0) shopName = spans[0].textContent.trim();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return [shopCode, shopName];
};

export const getTTQuantity = () => {
    let quantity = 1;
    const input = document.getElementById('J_IptAmount');
    if (input) quantity = +(input as HTMLInputElement).value;
    else {
        let inputs = document.getElementsByClassName('countValueForPC');
        if (inputs.length === 0) inputs = document.getElementsByClassName('Operation--countValue--3VF_tPH');
        if (inputs.length === 0) inputs = document.getElementsByClassName('countValue--mH0MCFR3');
        if (inputs.length === 0) inputs = document.getElementsByClassName('H7xLHMaqK8--countValue--_14c0d78');
        if (inputs.length === 0) inputs = document.getElementsByClassName('Oo3vRXl7BS--countValue--_14c0d78');
        if (inputs.length === 0) inputs = document.getElementsByClassName('fAIbwoPZg6--countValue--_14c0d78');
        if (inputs.length > 0) {
            quantity = +(inputs[0] as HTMLInputElement).value;
        }
    }
    return quantity;
};

export const getTTProducts = (link: string, comment: string) => {
    const products: Product[] = [];
    const { price, promotionPrice } = getTTPrices();
    if (price >= 0) {
        const { skuValues, skuLength, image } = getTTSkuValues();
        const [shopCode, shopName] = getTTShop();
        products.push({
            shopCode,
            shopName,
            link,
            image: image ?? getTTImage(),
            color: skuValues.length > 0 ? skuValues[0] : '',
            size: skuValues.length > 1 ? join(drop(skuValues), ',') : '',
            quantity: getTTQuantity(),
            cost: price,
            promotionCost: promotionPrice,
            comment,
            isCheckAttribute: skuLength > 0 && skuLength !== skuValues.length,
        });
    }
    return products;
};
