import { drop, join } from 'lodash';
import { ItemCost, Product } from '../types';
import { COST_TEXT } from './constant';

export const get1688Prices = () => {
    const prices: ItemCost[] = [];
    let priceItems = document.getElementsByClassName('step-price-item');
    if (priceItems.length === 0) priceItems = document.getElementsByClassName('price-wrapper');
    if (priceItems.length === 0) priceItems = document.getElementsByClassName('price-info');
    if (priceItems.length > 0) {
        let j = 0,
            cost1 = 0,
            endCost1 = 0;
        Array.from(priceItems).forEach((priceEle) => {
            j++;
            const spans = priceEle.getElementsByTagName('span');
            if (spans.length > 0) {
                let label = '',
                    priceText = '',
                    cost = 0,
                    endCost = 0,
                    i = 0;
                Array.from(spans).forEach((spanEle) => {
                    priceText += (spanEle as HTMLElement).innerText.trim();
                    if (spanEle.classList.contains('price-text')) {
                        i++;
                        if (i === 1) cost = +(spanEle as HTMLElement).innerText.trim();
                        if (i === 2) endCost = +(spanEle as HTMLElement).innerText.trim();
                    }
                    if (spanEle.classList.contains('unit-text')) {
                        label = spanEle.textContent.trim();
                    }
                });
                if (i > 0) prices.push({ label, cost, endCost });
                else {
                    priceText = priceText.replace('¥', '').trim();
                    if (j === 1) cost1 = +priceText;
                    else endCost1 = +priceText;
                }
            }
        });
        if (j > 0 && cost1 > 0 && endCost1 > 0) prices.push({ label: COST_TEXT, cost: cost1, endCost: endCost1 });
    }
    return {
        price: -1,
        promotionPrice: 0,
        prices,
    };
};

export const get1688Products = (link: string, comment: string) => {
    let color = '',
        mainImage = '',
        shopCode = '',
        shopName = '';
    const partProducts: Partial<Product>[] = [];
    let skuModuleWrappers = document.getElementsByClassName('sku-module-wrapper');
    if (skuModuleWrappers.length > 0) {
        let i = 0,
            j = 0;
        Array.from(skuModuleWrappers).forEach((skuModule) => {
            if (skuModule.classList.contains('sku-prop-module')) {
                i++;
                if (i === 1) {
                    const skuItems = skuModule.getElementsByClassName('prop-item-inner-wrapper');
                    if (skuItems.length > 0) {
                        Array.from(skuItems).forEach((skuItem) => {
                            if (skuItem.classList.contains('active')) {
                                const divItems = skuItem.getElementsByTagName('div');
                                if (divItems.length > 0) {
                                    Array.from(divItems).forEach((divItem) => {
                                        if (divItem.classList.contains('prop-img')) {
                                            mainImage = divItem.getAttribute('style');
                                        }
                                        if (divItem.classList.contains('prop-name')) {
                                            color = divItem.textContent.trim();
                                        }
                                    });
                                }
                            }
                        });
                    }
                }
            }
            if (skuModule.classList.contains('sku-scene-wrapper')) {
                j++;
                if (j == 1) {
                    const skuItemWrappers = skuModule.getElementsByClassName('sku-item-wrapper');
                    if (skuItemWrappers.length > 0) {
                        Array.from(skuItemWrappers).forEach((skuItem) => {
                            const inputs = skuItem.getElementsByTagName('input');
                            if (inputs.length > 0) {
                                const quantity = +inputs[0].value;
                                if (quantity > 0) {
                                    const divItems = skuItem.getElementsByTagName('div');
                                    let image = '',
                                        size = '',
                                        cost = 0;
                                    if (divItems.length > 0) {
                                        Array.from(divItems).forEach((divItem) => {
                                            const divClass = divItem.classList;
                                            if (divClass.contains('sku-item-image')) {
                                                image = divItem.getAttribute('style');
                                            }
                                            if (divClass.contains('sku-item-name')) {
                                                size = divItem.textContent.trim();
                                            }
                                            if (divClass.contains('discountPrice-price')) {
                                                cost = +divItem.textContent.replace('元', '').trim();
                                            }
                                        });
                                    }
                                    partProducts.push({
                                        quantity,
                                        image,
                                        size,
                                        cost,
                                    });
                                }
                            }
                        });
                    }
                }
            }
        });
        if (!mainImage) {
            mainImage = getMainImage();
        }
    } else {
        //https://detail.1688.com/offer/708308951081.html?spm=a260k.dacugeneral.home2019rec.39.415735e4WqQUgO&cosite=-&tracelog=p4p&_p_isad=1&clickid=060c38b2214947a3b874f24e75f24a82&sessionid=b1cbd478714ee3f5eab93f34193cd5d6
        mainImage = getMainImage();
        skuModuleWrappers = document.getElementsByClassName('next-table-body');
        if (skuModuleWrappers.length > 0) {
            const skuItemWrappers = skuModuleWrappers[0].getElementsByTagName('tr');
            if (skuItemWrappers.length > 0) {
                Array.from(skuItemWrappers).forEach((skuItem) => {
                    const inputs = skuItem.getElementsByTagName('input');
                    if (inputs.length > 0) {
                        const quantity = +inputs[0].value;
                        if (quantity > 0) {
                            const skuValues: string[] = [];
                            let cost = 0;
                            const priceItems = skuItem.getElementsByClassName('price');
                            if (priceItems.length > 0) cost = +priceItems[0].textContent.trim();
                            const spans = skuItem.getElementsByClassName('normal-text');
                            if (spans.length > 0) {
                                Array.from(spans).forEach((spanItem) => {
                                    skuValues.push(spanItem.textContent.trim());
                                });
                            }
                            partProducts.push({
                                quantity,
                                color: skuValues.length > 0 ? skuValues[0] : '',
                                size: skuValues.length > 1 ? join(drop(skuValues), ',') : '',
                                cost,
                            });
                        }
                    }
                });
            }
        } else {
            const skuItems = document.getElementsByClassName('expand-view-item');
            if (skuItems.length > 0) {
                let image = '';
                color = '';
                skuModuleWrappers = document.getElementsByClassName('sku-filter-button');
                if (skuModuleWrappers.length > 0) {
                    Array.from(skuModuleWrappers).forEach((skuModule) => {
                        if (skuModule.classList.contains('active')) {
                            const images = skuModule.getElementsByTagName('img');
                            if (images.length > 0) image = (images[0] as HTMLImageElement).currentSrc;
                            const divItem = skuModule.getElementsByClassName('label-name');
                            if (divItem.length > 0) color = divItem[0].textContent.trim();
                        }
                    });
                }
                Array.from(skuItems).forEach((skuItem) => {
                    const inputs = skuItem.getElementsByTagName('input');
                    const quantity = inputs.length > 0 ? +inputs[0].value : 0;
                    if (quantity > 0) {
                        let size = '',
                            cost = 0;
                        const images = skuItem.getElementsByTagName('img');
                        if (images.length > 0) image = (images[0] as HTMLImageElement).currentSrc;
                        const divItem = skuItem.getElementsByClassName('item-label');
                        if (divItem.length > 0) size = divItem[0].textContent.trim();
                        const priceItems = skuItem.getElementsByClassName('item-price-stock');
                        if (priceItems.length > 0) cost = +priceItems[0].textContent.replace('¥', '').trim();
                        partProducts.push({
                            quantity,
                            image,
                            color,
                            size,
                            cost,
                        });
                    }
                });
            }
            else{
                const skuItems = document.getElementsByClassName('single-price-warp');
                if (skuItems.length > 0) {
                    Array.from(skuItems).forEach((skuItem) => {
                        const inputs = skuItem.getElementsByTagName('input');
                        const quantity = inputs.length > 0 ? +inputs[0].value : 0;
                        if (quantity > 0) {
                            let cost = 0;
                            const priceItems = skuItem.getElementsByClassName('summary-num');
                            if (priceItems.length > 0) cost = +priceItems[0].textContent.replace('元', '').trim();
                            const spans = skuItem.getElementsByClassName('price-num');
                            if (spans.length > 1) color = spans[1].textContent.trim();
                            partProducts.push({
                                quantity,
                                image: '',
                                color,
                                size: '',
                                cost,
                            });
                        }
                    });
                }
            }
        }
    }
    if (partProducts.length > 0) {
        const shopEle = document.getElementById('hd_0_container_0');
        if (shopEle) {
            const spans = shopEle.getElementsByTagName('span');
            if (spans.length > 0) {
                shopName = spans[0].textContent.trim();
            }
        }
        let shops = document.getElementsByClassName('primary-row-link');
        if (shops.length > 0) {
            shopCode = (shops[0] as HTMLLinkElement).href;
        } else {
            shops = document.getElementsByClassName('shop-entry');
            if (shops.length > 0) {
                const aEle = shops[0].getElementsByTagName('a');
                if (aEle.length > 0) shopCode = (aEle[0] as HTMLAnchorElement).href;
            }
            //TODO: 1688shop.json - https://detail.1688.com/offer/622452295334.html?spm=a260k.dacugeneral.home2019rec.13.38c235e41IAyx3&&scm=1007.21237.280932.0&pvid=30fa878a-206d-430d-b177-5e23926667b9&object_id=622452295334&udsPoolId=2274586&resourceId=1797996&resultType=normal
        }
    }
    return partProducts.map((p) => {
        p.link = link;
        p.comment = comment;
        if (!p.color) p.color = color;
        if (!p.image) p.image = mainImage;
        p.shopCode = shopCode;
        p.shopName = shopName;
        p.promotionCost = p.cost;
        p.isCheckAttribute = false;
        return p;
    });
};

const getMainImage = () => {
    let images = document.getElementsByClassName('preview-img');
    if (images.length > 0) return (images[0] as HTMLImageElement).currentSrc;
    else {
        images = document.getElementsByClassName('detail-gallery-img');
        if (images.length > 0) return (images[0] as HTMLImageElement).currentSrc;
    }
    return '';
};
