import React, { useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import MainComponent from '../components/MainComponent';
import ConfigService from '../services/ConfigService';
import '../static/styles.css';
import { Site } from '../types';

const App: React.FC<{}> = () => {
    useEffect(() => {
        const url = window.location.href;
        let siteId = 0;
        if (url.match(/item.taobao/) || url.match(/taobao.com\/item\//)) {
            siteId = Site.TAOBAO;
        }
        if (url.match(/detail.tmall/) || url.match(/tmall.com\/item\//)) {
            siteId = Site.TMALL;
        }
        if (url.match(/detail.1688/) || url.match(/deal.1688/)) {
            siteId = Site.W1688;
        }
        if (url.match(/alibaba.com\/product-detail\//)) {
            siteId = Site.OTHER;
        }
        if (url.match(/mobile.yangkeduo.com/)) {
            siteId = Site.PINDUODUO;
        }
        if (siteId > 0) {
            const getExchange = async () => {
                const response = await ConfigService.getExtensionConfig(siteId);
                if (response.success) {
                    const container = document.createElement('div');
                    document.body.append(container);
                    const root = createRoot(container);
                    root.render(<MainComponent siteId={siteId} siteUrl={url} exchangeRate={response.data.exchangeRate} wrappers={response.data.wrappers} />);
                }
            };
            getExchange();
        }
    }, []);
    return <></>;
};

const container = document.createElement('div');
document.body.appendChild(container);
const root = createRoot(container);
root.render(<App />);
