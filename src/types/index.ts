export interface ApiResponse<T> {
    success: boolean;
    status: number;
    messages: string[];
    data: T;
}

export enum Site {
    TAOBAO = 1,
    TMALL = 2,
    W1688 = 3,
    OTHER = 4,
    PINDUODUO = 5,
}

export interface User {
    id?: number;
    name?: string;
    avatar?: string;
    isCustomer?: number;
    accessToken?: string;
    refreshToken?: string;
}
export interface Product {
    shopCode: string;
    shopName: string;
    link: string;
    image: string;
    color: string;
    size: string;
    quantity: number;
    cost: number;
    promotionCost: number;
    comment: string;
    isCheckAttribute: boolean;
}

export interface ItemCost {
    label: string;
    cost: number;
    endCost?: number;
}

export interface Order {
    id: number;
    code: string;
}

export interface SkuProp {
    pid: string;
    vid: string;
}

export interface Sku {
    id: string;
    name: string;
    name_vi: string;
    props_ids: string;
    //props_names: string;
    props: SkuProp[];
    color: string;
    size: string;
    quantity: number;
    salePrice: string;
    originPrice: string;
    image: string;
    stock: number;
}

export interface ProductDetail {
    itemId: number;
    productUrl: string;
    name: string;
    name_vi: string;
    images: string[];
    video: string;
    currency: string;
    priceInfo?: {
        price: string;
        originPrice: string;
    };
    skuPriceRange?: {
        beginNum: string;
        skuParam: string;
    };
    shopInfo: {
        shopId: string;
        shopName: string;
        shopUrl: string;
        shopLogo: string;
        isTmall: boolean;
    };
    skuProps: {
        pid: string;
        prop_name: string;
        values: {
            vid: string;
            name: string;
            imageUrl: string;
        }[];
    }[];
    skus: Sku[];
}