import React, { useEffect, useState } from 'react';
import { Order, Product, ProductDetail, Sku, User } from '../types';
import Prices from './Prices';
import classNames from 'classnames';
import { isEmpty, find, findIndex } from 'lodash';
import { AddShoppingCart, ExitToApp, LockOpen, ShoppingCart } from '@mui/icons-material';
import { AppBar, Avatar, Box, Button, IconButton, InputBase, Toolbar, Typography } from '@mui/material';
import NumberInput from './NumberInput';
import { ADMIN_URL } from '../utils/constant';
import OrderService from '../services/OrderService';

const ProductInfo: React.FC<{
    productDetail: ProductDetail;
    exchangeRate: number;
    user: User | undefined;
    order: Order | undefined;
    handleAuthed: () => void;
    showNotification: (success: boolean, text: string | string[]) => void;
}> = ({ productDetail, exchangeRate, user, order, handleAuthed, showNotification }) => {
    const [price, setPrice] = useState(0);
    const [quantity, setQuantity] = useState(1);
    const [comment, setComment] = useState('');
    const [sku, setSku] = useState<Sku>();
    const [linkOrderEdit, setLinkOrderEdit] = useState('');

    useEffect(() => {
        if (user) {
            if (order) {
                setLinkOrderEdit(
                    user.isCustomer === 2
                        ? `${ADMIN_URL}/customer/order/edit/${order.id}`
                        : `${ADMIN_URL}/order/edit/${order.id}`
                );
            } else {
                setLinkOrderEdit(
                    user.isCustomer === 2 ? `${ADMIN_URL}/customer/draffOrder` : `${ADMIN_URL}/draffOrder`
                );
            }
        } else {
            setLinkOrderEdit(`${ADMIN_URL}/customer/draffOrder`);
        }
    }, [user, order]);

    useEffect(() => {
        const skuItem = find(productDetail.skus, (item) => item.stock > 0);
        if (skuItem) {
            setSku(skuItem);
            setPrice(+skuItem.salePrice);
        } else {
            setSku(undefined);
            setPrice(+productDetail.priceInfo.price);
        }
    }, [productDetail]);

    const checkActive = (pid: string, vid: string) =>
        !!find(sku?.props, (item) => item.pid === pid && item.vid === vid);

    const chooseAttr = (pid: string, vid: string) => {
        const index = findIndex(sku.props, (p) => p.pid === pid);
        if (index > -1 && sku.props[index].vid !== vid) {
            const item = { ...sku };
            item.props[index].vid = vid;
            setSku(item);
        }
    };

    const addCart = async () => {
        if (!user) {
            handleAuthed();
            return;
        }
        if ((sku?.stock ?? 0) < quantity) {
            showNotification(false, 'Số lượng san phẩm không đủ');
            return;
        }
        let image = sku?.image,
            color = '',
            size = '';
        if (isEmpty(image)) {
            sku.props.forEach((p) => {
                const prop = find(productDetail.skuProps, (p1) => p1.pid === p.pid);
                if (prop) {
                    const value = find(prop.values, (v) => v.vid === p.vid);
                    if (value) image = value.imageUrl;
                    if (!isEmpty(image)) return false;
                }
            });
        }
        if (isEmpty(image)) image = productDetail.images[0] ?? '';
        sku.props.forEach((p, index) => {
            const prop = find(productDetail.skuProps, (p1) => p1.pid === p.pid);
            if (prop) {
                const value = find(prop.values, (v) => v.vid === p.vid);
                if (value) {
                    if (index === 0) color = value.name;
                    else size += (index > 1 ? ';' : '') + value.name;
                }
            }
        });
        const products: Product[] = [
            {
                shopCode: productDetail.shopInfo.shopId,
                shopName: productDetail.shopInfo.shopName,
                link: productDetail.productUrl,
                image,
                color,
                size,
                quantity,
                cost: price,
                promotionCost: price,
                comment,
                isCheckAttribute: false,
            },
        ];
        const response = await OrderService.addCart(products, order?.id ?? 0);
        showNotification(response.success, response.messages);
    };

    return (
        <>
            <AppBar position="static" elevation={0} sx={{ bgcolor: 'rgb(240, 88, 39)', marginBottom: '20px' }}>
                <Toolbar>
                    <img
                        src="https://chinavn.vn/assets/front/v3/img/logo.png"
                        alt="Đặt hàng 86"
                        style={{ height: 24, marginRight: 8 }}
                    />
                    <Box sx={{ flexGrow: 1 }} />
                    {user ? (
                        <>
                            <IconButton color="inherit" onClick={handleAuthed}>
                                <ExitToApp className="font16" />
                            </IconButton>
                            <Typography variant="body2" className="font16">
                                {user.name}
                            </Typography>
                            <Avatar alt={user.name} src={user.avatar} sx={{ marginLeft: 1 }} />
                        </>
                    ) : (
                        <Button color="inherit" className="font16" startIcon={<LockOpen />} onClick={handleAuthed}>
                            Đăng nhập
                        </Button>
                    )}
                </Toolbar>
            </AppBar>
            <h1>{productDetail.name_vi}</h1>
            <div className="panelPrice">
                <Prices
                    prices={[{ label: 'Giá', cost: price, endCost: 0 }]}
                    exchangeRate={exchangeRate}
                    orderCode={order?.code}
                    orderLink={linkOrderEdit}
                />
            </div>
            <table>
                <tbody>
                    {productDetail.skuProps.map((prop) => (
                        <tr key={prop.pid}>
                            <td className="attrName">{prop.prop_name}</td>
                            <td>
                                {prop.values.map((value) => (
                                    <span
                                        key={value.vid}
                                        className={classNames('attrValue', {
                                            active: checkActive(prop.pid, value.vid),
                                        })}
                                        onClick={() => chooseAttr(prop.pid, value.vid)}
                                    >
                                        {value.imageUrl && <img src={value.imageUrl} alt="" />}
                                        {value.name}
                                    </span>
                                ))}
                            </td>
                        </tr>
                    ))}
                    {sku && (
                        <>
                            <tr>
                                <td className="attrName">Số lượng</td>
                                <td>
                                    <NumberInput
                                        min={1}
                                        max={sku?.stock ?? 1}
                                        value={quantity}
                                        onChange={(_, v) => setQuantity(v)}
                                    />
                                </td>
                            </tr>
                            <tr>
                                <td className="attrName">Ghi chú</td>
                                <td>
                                    <InputBase
                                        value={comment}
                                        onChange={(e) => setComment(e.target.value)}
                                        placeholder="Ghi chú..."
                                        className="font16"
                                        fullWidth
                                        sx={{ backgroundColor: 'white', padding: '3px', border: '1px solid #dadde0' }}
                                    />
                                </td>
                            </tr>
                            <tr>
                                <td colSpan={2}>
                                    <Box p={2} display="flex" justifyContent="space-between">
                                        <Button
                                            variant="contained"
                                            sx={{
                                                color: '#fff',
                                                backgroundColor: '#ff5000',
                                                '&:hover': {
                                                    backgroundColor: '#ff5000',
                                                },
                                            }}
                                            className="font16"
                                            startIcon={<AddShoppingCart />}
                                            onClick={addCart}
                                        >
                                            Đặt hàng
                                        </Button>
                                        <Button
                                            color="inherit"
                                            component="a"
                                            href={linkOrderEdit}
                                            target="_blank"
                                            className="font16"
                                            startIcon={<ShoppingCart />}
                                            sx={{
                                                color: '#fff',
                                                backgroundColor: '#ff5000',
                                                '&:hover': {
                                                    backgroundColor: '#ff5000',
                                                },
                                            }}
                                        >
                                            Xem {order ? order.code : 'đơn hàng'}
                                        </Button>
                                    </Box>
                                </td>
                            </tr>
                        </>
                    )}
                </tbody>
            </table>
        </>
    );
};

export default ProductInfo;
