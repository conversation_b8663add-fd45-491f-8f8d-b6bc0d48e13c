import React from 'react';
import { ItemCost } from '../types';
import { OLD_COST_TEXT } from '../utils/constant';

const Prices: React.FC<{
    prices: ItemCost[];
    exchangeRate: number;
    orderCode?: string;
    orderLink?: string;
}> = ({ prices, exchangeRate, orderCode, orderLink }) => {
    return prices.length > 0 ? (
        <>
            <div className="column">
                <div className="title">Tỉ giá</div>
                <div className="value">{exchangeRate.toLocaleString()}</div>
            </div>
            {prices
                .filter((item) => item.cost >= 0 && item.label !== OLD_COST_TEXT)
                .map((item, index) => (
                    <div className="column" key={index}>
                        <div className="title">{item.label}</div>
                        <div className="value">
                            {(exchangeRate * item.cost).toLocaleString()}đ
                            {item.endCost > 0 &&
                                item.endCost !== item.cost &&
                                ` - ${(exchangeRate * item.endCost).toLocaleString()}đ`}
                        </div>
                    </div>
                ))}
            {orderCode && orderLink && (
                <div className="column">
                    <div className="title">
                        Đơn hàng
                    </div>
                    <div className="value">
                        <a href={orderLink} target="_blank" rel="noopener noreferrer">
                            {orderCode}
                        </a>
                    </div>
                </div>
            )}
        </>
    ) : (
        <></>
    );
};

export default Prices;
