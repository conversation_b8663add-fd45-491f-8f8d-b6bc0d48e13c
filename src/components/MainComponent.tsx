import { includes, isArray, isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import LoggerService from '../services/LoggerService';
import OrderService from '../services/OrderService';
import ProductExternalService from '../services/ProductExternalService';
import UserService from '../services/UserService';
import { ItemCost, Order, Product, ProductDetail, Site, User } from '../types';
import { getTTProducts } from '../utils';
import { get1688Prices, get1688Products } from '../utils/1688';
import { TIMEOUT_PRICE_PANEL } from '../utils/constant';
import { getPinduoduoPrices, getPinduoduoProducts } from '../utils/pinduoduo';
import { getToken, getUserData, removeUserData, setToken, setUserData } from '../utils/storage';
import CartPanel from './CartPanel';
import LoginModal from './LoginModal';
import NotificationModal from './NotificationModal';
import Prices from './Prices';

const MainComponent: React.FC<{ siteId: number; siteUrl: string; exchangeRate: number; wrappers: string[] }> = ({
    siteId,
    siteUrl,
    exchangeRate,
    wrappers,
}) => {
    const [user, setUser] = useState<User>();
    const [order, setOrder] = useState<Order>();
    const [productDetail, setProductDetail] = useState<ProductDetail>();
    const [comment, setComment] = useState('');
    const [openLogin, setOpenLogin] = useState(false);
    const [isAddCart, setIsAddCart] = useState(false);
    const [openNotification, setOpenNotification] = useState(false);
    const [isSuccessNotification, setIsSuccessNotification] = useState(false);
    const [contentNotification, setContentNotification] = useState('');

    const showNotification = (success: boolean, text: string | string[]) => {
        if (!isEmpty(text)) {
            setIsSuccessNotification(success);
            setContentNotification(isArray(text) ? text[0] : (text as string));
            setOpenNotification(true);
        }
    };

    const addCart = async (c: string, orderId: number) => {
        let products: Product[] = [];
        if (includes([Site.TMALL, Site.TAOBAO], siteId)) {
            products = getTTProducts(siteUrl, c);
        }
        if (siteId === Site.W1688) {
            //@ts-ignore
            products = get1688Products(siteUrl, c);
        }
        if (siteId === Site.PINDUODUO) {
            products = getPinduoduoProducts(siteUrl, c);
        }
        if (products.length > 0) {
            if (products[0].isCheckAttribute) {
                showNotification(false, 'Vui lòng chọn thuộc tính sản phẩm');
            } else {
                const response = await OrderService.addCart(products, orderId);
                showNotification(response.success, response.messages);
            }
        } else {
            LoggerService.log(`Tool::Can't GET Product ${siteUrl}`);
        }
    };

    const onAddCart = async (c: string) => {
        setComment(c);
        if (!user) {
            setOpenLogin(true);
            setIsAddCart(true);
            return;
        }
        addCart(c, order ? order.id : 0);
    };

    const handleLogout = async () => {
        setUser(undefined);
        setOrder(undefined);
        removeUserData();
        UserService.logout();
    };

    const handleReLogin = async () => {
        const refreshToken = await getToken(false);
        let flag = !!refreshToken;
        if (flag) {
            const response = await UserService.getToken(refreshToken);
            if (response.success) {
                await setToken(response.data.accessToken, response.data.refreshToken);
                const result = await UserService.getProfile();
                if (result.success) {
                    onLogin(result.data);
                } else flag = false;
            } else flag = false;
        }
        if (!flag) handleLogout();
    };

    const getOrderDraff = async (hasAddCart: boolean) => {
        const response = await OrderService.getListDraff();
        let orderId = 0;
        if (response.success) {
            const items = response.data.items;
            if (items.length > 0) {
                setOrder(items[0]);
                orderId = items[0].id;
            } else {
                const result = await OrderService.createEmpty();
                if (result.success) {
                    setOrder(result.data);
                    orderId = result.data.id;
                }
            }
            if (orderId > 0) {
                if (hasAddCart) {
                    setIsAddCart(false);
                    addCart(comment, orderId);
                }
            } else {
                LoggerService.log(`Tool::Can't GET init order ${siteUrl}`);
            }
        } else if (response.status === 401) {
            handleReLogin();
        }
    };

    const onLogin = (u: User) => {
        setUser(u);
        setUserData(u.name, u.avatar, u.accessToken, u.refreshToken).then(() => {
            getOrderDraff(isAddCart);
        });
    };

    /*const getTTPriceWrapper = () => {
        let result: HTMLCollectionOf<Element> | null = null;
        wrappers.forEach((wrapper) => {
            if (!result) {
                const priceWrappers = document.getElementsByClassName(wrapper);
                if (priceWrappers.length > 0) {
                    result = priceWrappers;
                }
            }
        });
        return result;
    };

    const appendTTData = (priceWrappers: HTMLCollectionOf<Element> | null, pd: ProductDetail) => {
        if (priceWrappers) {
            const container = document.createElement('div');
            container.className = 'dh86MainProduct';
            priceWrappers[0].setAttribute('style', 'position: relative;');
            priceWrappers[0].innerHTML = '';
            priceWrappers[0].appendChild(container);
            const root = createRoot(container);
            root.render(
                <ProductInfo
                    productDetail={pd}
                    exchangeRate={exchangeRate}
                    user={user}
                    order={order}
                    handleAuthed={() => {
                        if (!user) setOpenLogin(true);
                        else handleLogout();
                    }}
                    showNotification={showNotification}
                />
            );
        }
    };*/

    useEffect(() => {
        const getUser = async () => {
            const userData = await getUserData();
            if (!isEmpty(userData)) {
                setUser(userData);
                getOrderDraff(false);
            }
        };
        getUser();

        const appendPrices = (priceWrappers: HTMLCollectionOf<Element>, prices: ItemCost[] = []) => {
            if (priceWrappers.length > 0) {
                const container = document.createElement('div');
                container.className = 'panelPrice';
                priceWrappers[0].append(container);
                const root = createRoot(container);
                root.render(<Prices prices={prices} exchangeRate={exchangeRate} />);
            }
        };

        // const getTTPriceWrapper = () => {
        //     let priceWrappers = document.getElementsByClassName('tb-promo-meta');
        //     if (priceWrappers.length === 0) {
        //         priceWrappers = document.getElementsByClassName('Price--root--1CrVGjc');
        //         if (priceWrappers.length === 0) {
        //             priceWrappers = document.getElementsByClassName('tb-meta');
        //             if (priceWrappers.length === 0) {
        //                 priceWrappers = document.getElementsByClassName('SecurityPrice--securityPrice--25lJx-X');
        //                 if (priceWrappers.length === 0) {
        //                     priceWrappers = document.getElementsByClassName('Price--root--1X-r-XP');
        //                     if (priceWrappers.length === 0) {
        //                         priceWrappers = document.getElementsByClassName('Price--eXtPHNRb');
        //                         if (priceWrappers.length === 0) {
        //                             priceWrappers = document.getElementsByClassName('displayPrice--oD54EJg0');
        //                             if (priceWrappers.length === 0) {
        //                                 priceWrappers = document.getElementsByClassName(
        //                                     'E7gD8doUq1--displayPrice--_475cc3b'
        //                                 );
        //                                 if (priceWrappers.length === 0) {
        //                                     priceWrappers = document.getElementsByClassName(
        //                                         'H7xLHMaqK8--displayPrice--_475cc3b'
        //                                     );
        //                                     if (priceWrappers.length === 0) {
        //                                         priceWrappers = document.getElementsByClassName(
        //                                             'HU5oZkMwJv--displayPrice--_475cc3b'
        //                                         );
        //                                         if (priceWrappers.length === 0) {
        //                                             priceWrappers = document.getElementsByClassName(
        //                                                 'Oo3vRXl7BS--displayPrice--_475cc3b'
        //                                             );
        //                                             if (priceWrappers.length === 0) {
        //                                                 priceWrappers = document.getElementsByClassName(
        //                                                     'fAIbwoPZg6--displayPrice--_475cc3b'
        //                                                 );
        //                                             }
        //                                         }
        //                                     }
        //                                 }
        //                             }
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        //     return priceWrappers;
        // };

        const getProductId = (pLink: string) => {
            const link = new URL(pLink);
            if (link.host === 'detail.1688.com') {
                const list = link.pathname.split('/');
                if (list.length > 0) {
                    const item = list[list.length - 1];
                    return item.slice(0, item.length - 5);
                }
            } else {
                const searchParams = new URLSearchParams(link.search);
                const paramsId = searchParams.get('id');
                if (paramsId) return paramsId;
            }
            return null;
        };

        const getProductDetail = async (pId: string) => {
            const response = await ProductExternalService.getDetail(1, pId);
            setProductDetail(response.data ?? undefined);
            // if (response.success) {
            //     appendTTData(getTTPriceWrapper(), response.data);
            // }
        };

        const timeOut = setTimeout(() => {
            if (includes([Site.TMALL, Site.TAOBAO], siteId)) {
                const pId = getProductId(siteUrl);
                if (pId) getProductDetail(pId);

                // const { prices } = getTTPrices();
                // if (prices.length > 0) {
                //     appendPrices(getTTPriceWrapper(), prices);
                // }
            }
            if (siteId === Site.W1688) {
                const { prices } = get1688Prices();
                if (prices.length > 0) {
                    let priceWrappers = document.getElementsByClassName('od-pc-offer-price-contain');
                    if (priceWrappers.length === 0) {
                        priceWrappers = document.getElementsByClassName('module-od-main-price');
                    }
                    appendPrices(priceWrappers, prices);
                }
            }
            if (siteId === Site.PINDUODUO) {
                const { prices } = getPinduoduoPrices();
                if (prices.length > 0) {
                    let priceWrappers = document.getElementsByClassName('TtX4-Ajj');
                    if (priceWrappers.length === 0) {
                        priceWrappers = document.getElementsByClassName('TtX4-Ajjwvko19ZAZKBMR');
                    }
                    appendPrices(priceWrappers, prices);
                }
            }
        }, TIMEOUT_PRICE_PANEL);
        return () => clearTimeout(timeOut);
    }, [siteId, siteUrl]);

    // useEffect(() => {
    //     if (productDetail) {
    //         appendTTData(getTTPriceWrapper(), productDetail);
    //     }
    // }, [user, order, productDetail]);

    return (
        <>
            <CartPanel
                exchangeRate={exchangeRate}
                user={user}
                order={order}
                productDetail={productDetail}
                onAddCart={onAddCart}
                handleLogout={handleLogout}
                handleLogin={() => setOpenLogin(true)}
                showNotification={showNotification}
            />
            <LoginModal open={openLogin} onClose={() => setOpenLogin(false)} onLogin={onLogin} />
            <NotificationModal
                open={openNotification}
                onClose={() => setOpenNotification(false)}
                isSuccess={isSuccessNotification}
                content={contentNotification}
            />
        </>
    );
};

export default MainComponent;
