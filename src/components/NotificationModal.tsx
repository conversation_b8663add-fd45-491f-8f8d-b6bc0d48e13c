import { Button, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import React, { useEffect } from 'react';
import { TIMEOUT_MESSAGE } from '../utils/constant';

const NotificationModal: React.FC<{
    open: boolean;
    onClose: () => void;
    isSuccess: boolean;
    content: string;
}> = ({ open, onClose, isSuccess, content }) => {
    useEffect(() => {
        let timeoutId;
        if (open) {
            timeoutId = setTimeout(() => {
                onClose();
            }, TIMEOUT_MESSAGE);
        }
        return () => clearTimeout(timeoutId);
    }, [open, onClose]);

    return (
        <Dialog style={{ zIndex: 999999 }} open={open} onClose={onClose}>
            <DialogTitle
                className="font16"
                style={{
                    color: isSuccess ? 'green' : 'red',
                }}
            >
                Thông báo
            </DialogTitle>
            <DialogContent>
                <Typography className="font16" color={isSuccess ? 'green' : 'red'}>
                    {content}
                </Typography>
            </DialogContent>
            <DialogActions>
                <Button className="font16" onClick={onClose} sx={{ color: isSuccess ? 'green' : 'red' }}>
                    Đóng
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default NotificationModal;
