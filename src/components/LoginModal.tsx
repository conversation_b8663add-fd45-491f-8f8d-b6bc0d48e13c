import { Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react';
import UserService from '../services/UserService';
import { User } from '../types';
import { TIMEOUT_MESSAGE } from '../utils/constant';

const LoginModal: React.FC<{
    open: boolean;
    onClose: () => void;
    onLogin: (u: User) => void;
}> = ({ open, onClose, onLogin }) => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [message, setMessage] = useState('');

    const handleLogin = async () => {
        if (!!username && !!password) {
            const response = await UserService.login(username, password);
            if (response.success) {
                onLogin(response.data);
                onClose();
            } else setMessage(response.messages[0]);
        } else setMessage('<PERSON><PERSON><PERSON> trường không được bỏ trống');
    };

    useEffect(() => {
        let timeoutId;
        if (message !== '') {
            timeoutId = setTimeout(() => {
                setMessage('');
            }, TIMEOUT_MESSAGE);
        }
        return () => clearTimeout(timeoutId);
    }, [message]);

    return (
        <Dialog open={open} onClose={onClose} sx={{ zIndex: 99999999999 }}>
            <DialogTitle className="font16" sx={{ bgcolor: 'rgb(240, 88, 39)', color: '#fff' }}>
                Đăng nhập
            </DialogTitle>
            <DialogContent sx={{ bgcolor: 'rgb(240, 88, 39)' }}>
                <TextField
                    variant="outlined"
                    fullWidth
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    margin="normal"
                    placeholder="Điện thoại"
                    className="font16"
                    sx={{ backgroundColor: '#fff' }}
                />
                <TextField
                    type="password"
                    variant="outlined"
                    fullWidth
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    margin="normal"
                    placeholder="Mật khẩu"
                    className="font16"
                    sx={{ backgroundColor: '#fff' }}
                />
            </DialogContent>
            <DialogActions sx={{ bgcolor: 'rgb(240, 88, 39)' }}>
                <Button
                    onClick={onClose}
                    color="primary"
                    className="font16"
                    sx={{ color: '#fff', marginRight: 'auto', textTransform: 'none' }}
                >
                    {message === '' ? 'Đóng' : message}
                </Button>
                <Button
                    variant="contained"
                    sx={{
                        color: 'rgb(30, 134, 144)',
                        backgroundColor: 'rgb(255, 237, 34)',
                        '&:hover': {
                            backgroundColor: 'rgb(204, 188, 0)',
                        },
                    }}
                    onClick={handleLogin}
                    className="font16"
                >
                    Đăng nhập
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default LoginModal;
