import { AddShoppingCart, ExitToApp, LockOpen, ShoppingCart, Close, ArrowDropUpOutlined } from '@mui/icons-material';
import { AppBar, Avatar, Box, Button, IconButton, InputBase, Toolbar, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { isEmpty, find, findIndex, ceil } from 'lodash';
import { Order, Product, ProductDetail, Sku, SkuProp, User } from '../types';
import { ADMIN_URL } from '../utils/constant';
import classNames from 'classnames';
import NumberInput from './NumberInput';
import OrderService from '../services/OrderService';

const CartPanel: React.FC<{
    exchangeRate: number;
    user: User | undefined;
    order: Order | undefined;
    productDetail: ProductDetail | undefined;
    handleLogout: () => void;
    handleLogin: () => void;
    onAddCart: (c: string) => void;
    showNotification: (success: boolean, text: string | string[]) => void;
}> = ({ exchangeRate, user, order, productDetail, handleLogout, handleLogin, onAddCart, showNotification }) => {
    const [comment, setComment] = useState('');
    const [price, setPrice] = useState(0);
    const [quantity, setQuantity] = useState(1);
    const [sku, setSku] = useState<Sku>();
    const [props, setProps] = useState<SkuProp[]>([]);
    const [linkOrderEdit, setLinkOrderEdit] = useState('');
    const [minimized, setMinimized] = useState(false);

    useEffect(() => {
        if (user) {
            if (order) {
                setLinkOrderEdit(
                    user.isCustomer === 2
                        ? `${ADMIN_URL}/customer/order/edit/${order.id}`
                        : `${ADMIN_URL}/order/edit/${order.id}`
                );
            } else {
                setLinkOrderEdit(
                    user.isCustomer === 2 ? `${ADMIN_URL}/customer/draffOrder` : `${ADMIN_URL}/draffOrder`
                );
            }
        } else {
            setLinkOrderEdit(`${ADMIN_URL}/customer/draffOrder`);
        }
    }, [user, order]);

    useEffect(() => {
        if (productDetail) {
            const skuItem = find(productDetail.skus, (item) => item.stock > 0);
            if (skuItem) {
                setSku(skuItem);
                setPrice(+skuItem.salePrice);
                setProps(skuItem.props);
            } else {
                setSku(undefined);
                setPrice(+productDetail.priceInfo.price);
                setProps([]);
            }
        } else {
            setSku(undefined);
            setPrice(0);
            setProps([]);
        }
    }, [productDetail]);

    const checkEqual = (props1: SkuProp[], props2: SkuProp[]) => {
        if(props1.length !== props2.length) return false;
        return props1.every((p1) => find(props2, { pid: p1.pid, vid: p1.vid }));
    };

    const chooseAttr = (pid: string, vid: string) => {
        const items = [...props];
        const idx = findIndex(items, { pid });
        if (idx > -1) {
            items[idx].vid = vid;
            setProps(items);
        }
        productDetail.skus.forEach((_sku) => {
            if(checkEqual(items, _sku.props)) {
                setSku(_sku);
                setPrice(+_sku.salePrice);
                return false;
            }
        });
    };

    const addCart = async () => {
        if (!user) {
            handleLogin();
            return;
        }
        if ((sku?.stock ?? 0) < quantity) {
            showNotification(false, 'Số lượng san phẩm không đủ');
            return;
        }
        let image = '',
            color = '',
            size = '';
        if (isEmpty(image)) {
            sku.props.forEach((p) => {
                const prop = find(productDetail.skuProps, (p1) => p1.pid === p.pid);
                if (prop) {
                    const value = find(prop.values, (v) => v.vid === p.vid);
                    if (value && isEmpty(image)) image = value.imageUrl;
                    if (!isEmpty(image)) return false;
                }
            });
        }
        if (isEmpty(image)) image = sku?.image ?? (productDetail.images[0] ?? '');
        sku.props.forEach((p, index) => {
            const prop = find(productDetail.skuProps, (p1) => p1.pid === p.pid);
            if (prop) {
                const value = find(prop.values, (v) => v.vid === p.vid);
                if (value) {
                    if (index === 0) color = value.name;
                    else size += (index > 1 ? ';' : '') + value.name;
                }
            }
        });
        const products: Product[] = [
            {
                shopCode: productDetail.shopInfo.shopId,
                shopName: productDetail.shopInfo.shopName,
                link: productDetail.productUrl,
                image,
                color,
                size,
                quantity,
                cost: price,
                promotionCost: price,
                comment,
                isCheckAttribute: false,
            },
        ];
        const response = await OrderService.addCart(products, order?.id ?? 0);
        showNotification(response.success, response.messages);
    };

    return (
        <Box
            sx={{
                position: 'fixed',
                bottom: 0,
                right: 0,
                maxWidth: 550,
                width: '100%',
                bgcolor: 'rgb(240, 88, 39)',
                color: 'white',
                boxShadow: '0px -4px 8px rgba(0, 0, 0, 0.1)',
                zIndex: 9999999999,
                height: 'auto',
            }}
        >
            <AppBar position="static" elevation={0} sx={{ bgcolor: 'rgb(240, 88, 39)' }}>
                <Toolbar>
                    <img
                        src="https://chinavn.vn/assets/front/v3/img/logo.png"
                        alt="Đặt hàng 86"
                        style={{ height: 24, marginRight: 8 }}
                        className="cursor-pointer"
                        onClick={() => {
                            if (minimized) setMinimized(false);
                        }}
                    />
                    {price > 0 && (
                        <>
                            <Box sx={{ flexGrow: 1 }} />
                            <Typography
                                variant="body2"
                                className="font20 cursor-pointer"
                                onClick={() => {
                                    if (minimized) setMinimized(false);
                                }}
                            >
                                {ceil(exchangeRate * price).toLocaleString()}đ
                            </Typography>
                        </>
                    )}
                    <Box sx={{ flexGrow: 1 }} />
                    {user ? (
                        <>
                            <IconButton color="inherit" onClick={handleLogout}>
                                <ExitToApp className="font16" />
                            </IconButton>
                            <Typography variant="body2" className="font16">
                                {user.name}
                            </Typography>
                            <Avatar alt={user.name} src={user.avatar} sx={{ marginLeft: 1 }} />
                        </>
                    ) : (
                        <Button color="inherit" className="font16" startIcon={<LockOpen />} onClick={handleLogin}>
                            Đăng nhập
                        </Button>
                    )}
                    <IconButton color="inherit" onClick={() => setMinimized(!minimized)} sx={{ marginLeft: 1 }}>
                        {minimized ? <ArrowDropUpOutlined className="font16" /> : <Close className="font16" />}
                    </IconButton>
                </Toolbar>
            </AppBar>

            {!minimized && (
                <>
                    {productDetail && (
                        <div className="dh86MainProduct">
                            <table>
                                <tbody>
                                    {productDetail.skuProps.map((prop) => (
                                        <tr key={prop.pid}>
                                            <td className="attrName">{prop.prop_name}</td>
                                            <td>
                                                {prop.values.map((value) => (
                                                    <span
                                                        key={value.vid}
                                                        className={classNames('attrValue', {
                                                            active: !!find(props, { pid: prop.pid, vid: value.vid }),
                                                        })}
                                                        onClick={() => chooseAttr(prop.pid, value.vid)}
                                                    >
                                                        {value.imageUrl && <img src={value.imageUrl} alt="" />}
                                                        {value.name}
                                                    </span>
                                                ))}
                                            </td>
                                        </tr>
                                    ))}
                                    {sku && (
                                        <>
                                            <tr>
                                                <td className="attrName">Số lượng</td>
                                                <td>
                                                    <NumberInput
                                                        min={1}
                                                        max={sku?.stock ?? 1}
                                                        value={quantity}
                                                        onChange={(_, v) => setQuantity(v)}
                                                    />
                                                </td>
                                            </tr>
                                        </>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    )}

                    {!productDetail && (
                        <Box p={2}>
                            <InputBase
                                value={comment}
                                onChange={(e) => setComment(e.target.value)}
                                placeholder="Ghi chú..."
                                className="font16"
                                fullWidth
                                sx={{ backgroundColor: 'white', padding: '10px' }}
                            />
                        </Box>
                    )}

                    <Box p={2} display="flex" justifyContent="space-between">
                        <Button
                            variant="contained"
                            sx={{
                                color: 'rgb(30, 134, 144)',
                                backgroundColor: 'rgb(255, 237, 34)',
                                '&:hover': {
                                    backgroundColor: 'rgb(204, 188, 0)',
                                },
                            }}
                            className="font16"
                            startIcon={<AddShoppingCart />}
                            onClick={() => (productDetail ? addCart() : onAddCart(comment))}
                        >
                            Đặt hàng
                        </Button>

                        <Button
                            color="inherit"
                            component="a"
                            href={linkOrderEdit}
                            target="_blank"
                            className="font16"
                            startIcon={<ShoppingCart />}
                            sx={{ color: 'white !important' }}
                        >
                            Xem {order ? order.code : 'đơn hàng'}
                        </Button>
                    </Box>
                </>
            )}
        </Box>
    );
};

export default CartPanel;
